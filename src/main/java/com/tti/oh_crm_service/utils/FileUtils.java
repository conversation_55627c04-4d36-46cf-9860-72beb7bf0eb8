package com.tti.oh_crm_service.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.codec.multipart.FilePart;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

@Slf4j
public class FileUtils {
    
    // Media file extensions (images and videos)
    private static final List<String> MEDIA_EXTENSIONS = Arrays.asList(
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg", ".tiff", ".tif",
        ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv", ".m4v", ".3gp"
    );
    
    // Media content types
    private static final List<String> MEDIA_CONTENT_TYPES = Arrays.asList(
        "image/", "video/"
    );
    
    // Maximum file size (100MB)
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024;

    /**
     * Determines if a file is a media file (image or video)
     * @param contentType The content type from the file part
     * @param fileName The original filename
     * @return true if the file is a media file
     */
    public static boolean isMediaFile(String contentType, String fileName) {
        // Check content type first
        if (contentType != null) {
            for (String mediaType : MEDIA_CONTENT_TYPES) {
                if (contentType.startsWith(mediaType)) {
                    return true;
                }
            }
        }
        
        // Fallback to file extension check
        if (fileName != null) {
            String lowerFileName = fileName.toLowerCase();
            return MEDIA_EXTENSIONS.stream()
                .anyMatch(lowerFileName::endsWith);
        }
        
        return false;
    }
    
    /**
     * Gets the file category (media or file)
     * @param contentType The content type
     * @param fileName The filename
     * @return "media" or "file"
     */
    public static String getFileCategory(String contentType, String fileName) {
        return isMediaFile(contentType, fileName) ? "media" : "file";
    }
    
    /**
     * Validates filename for security and format
     * @param fileName The filename to validate
     * @return null if valid, error message if invalid
     */
    public static String validateFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "Filename cannot be empty";
        }
        
        // Check for dangerous characters
        if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
            return "Filename contains invalid characters: " + fileName;
        }
        
        // Check filename length
        if (fileName.length() > 255) {
            return "Filename too long: " + fileName;
        }
        
        return null;
    }
    
    /**
     * Validates file size
     * @param fileSize The file size in bytes
     * @return null if valid, error message if invalid
     */
    public static String validateFileSize(long fileSize) {
        if (fileSize <= 0) {
            return "File is empty";
        }
        
        if (fileSize > MAX_FILE_SIZE) {
            return "File size exceeds maximum allowed size of " + formatFileSize(MAX_FILE_SIZE);
        }
        
        return null;
    }
    
    /**
     * Gets the file extension from filename
     * @param fileName The filename
     * @return The file extension including the dot, or empty string if no extension
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex);
    }
    
    /**
     * Gets the base filename without extension
     * @param fileName The filename
     * @return The base filename without extension
     */
    public static String getBaseFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return fileName;
        }
        
        return fileName.substring(0, lastDotIndex);
    }
    
    /**
     * Generates a unique filename using the format: originalName_timestamp.extension
     * @param originalFileName The original filename
     * @return A unique filename
     */
    public static String generateUniqueFileName(String originalFileName) {
        String fileExtension = getFileExtension(originalFileName);
        String baseFileName = getBaseFileName(originalFileName);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));
        
        return baseFileName + "_" + timestamp + fileExtension;
    }
    
    /**
     * Creates the upload directory structure for a module
     * @param module The module name (e.g., PLAYBOOK, LEAD)
     * @param category The file category ("media" or "file")
     * @return The created directory path
     */
    public static Path createModuleDirectory(String module, String category) {
        // Use absolute path resolution to handle Docker container environment
        Path uploadsDir = resolveUploadsDirectory();
        Path categoryDir = uploadsDir.resolve(category + "s"); // "medias" or "files"
        Path moduleDir = categoryDir.resolve(module.toUpperCase());

        try {
            log.info("Creating directory structure: {} (absolute: {})", moduleDir, moduleDir.toAbsolutePath());

            // Check base uploads directory permissions first
            if (!Files.exists(uploadsDir)) {
                log.error("Base uploads directory does not exist: {}", uploadsDir.toAbsolutePath());
                throw new IOException("Base uploads directory does not exist: " + uploadsDir.toAbsolutePath());
            }

            // Create category directory if it doesn't exist
            if (!Files.exists(categoryDir)) {
                log.info("Creating category directory: {}", categoryDir.toAbsolutePath());
                try {
                    Files.createDirectory(categoryDir);
                } catch (IOException e) {
                    log.error("Failed to create category directory {}: {}", categoryDir.toAbsolutePath(), e.getMessage());
                    throw new IOException("Failed to create category directory: " + e.getMessage(), e);
                }
            }

            // Create module directory if it doesn't exist
            if (!Files.exists(moduleDir)) {
                log.info("Creating module directory: {}", moduleDir.toAbsolutePath());
                try {
                    Files.createDirectory(moduleDir);
                } catch (IOException e) {
                    log.error("Failed to create module directory {}: {}", moduleDir.toAbsolutePath(), e.getMessage());
                    throw new IOException("Failed to create module directory: " + e.getMessage(), e);
                }
            }

            log.info("Successfully ensured directory structure exists: {}", moduleDir.toAbsolutePath());

            // Verify the directory is writable
            if (!Files.isWritable(moduleDir)) {
                throw new IOException("Directory is not writable: " + moduleDir.toAbsolutePath());
            }

            return moduleDir;
        } catch (IOException e) {
            log.error("Failed to create directory structure {} (absolute: {}): {}",
                moduleDir, moduleDir.toAbsolutePath(), e.getMessage(), e);
            throw new RuntimeException("Failed to create upload directory: " + e.getMessage(), e);
        }
    }

    /**
     * Resolves the uploads directory path, handling both local development and Docker container environments
     * @return The resolved uploads directory path
     */
    private static Path resolveUploadsDirectory() {
        // First try the current working directory (for local development)
        Path localUploads = Paths.get("uploads");

        // Check if we're running in Docker container (working directory is /app)
        String workingDir = System.getProperty("user.dir");
        log.debug("Current working directory: {}", workingDir);

        if (workingDir != null && workingDir.equals("/app")) {
            // We're in Docker container, use the mounted volume path
            Path dockerUploads = Paths.get("/app/uploads");
            log.debug("Detected Docker environment, using: {}", dockerUploads.toAbsolutePath());
            return dockerUploads;
        } else {
            // Local development environment
            log.debug("Using local development path: {}", localUploads.toAbsolutePath());
            return localUploads;
        }
    }
    
    /**
     * Saves a FilePart to the specified path
     * @param filePart The file part to save
     * @param targetPath The target path to save to
     * @return The file size in bytes, or -1 if failed
     */
    public static long saveFilePart(FilePart filePart, Path targetPath) {
        try (OutputStream outputStream = Files.newOutputStream(targetPath, StandardOpenOption.CREATE, StandardOpenOption.WRITE)) {
            
            // Collect all data buffers and write them synchronously
            DataBuffer dataBuffer = DataBufferUtils.join(filePart.content()).block();
            if (dataBuffer != null) {
                try (InputStream inputStream = dataBuffer.asInputStream()) {
                    inputStream.transferTo(outputStream);
                } finally {
                    DataBufferUtils.release(dataBuffer);
                }
            }

            // Get and return file size
            long fileSize = Files.size(targetPath);
            log.debug("Successfully saved file: {} (size: {} bytes)", targetPath, fileSize);
            return fileSize;
        } catch (Exception e) {
            log.error("Failed to save file {}: {}", targetPath, e.getMessage());
            return -1L;
        }
    }
    
    /**
     * Gets the content type from FilePart, with fallback to empty string
     * @param filePart The file part
     * @return The content type or empty string
     */
    public static String getContentType(FilePart filePart) {
        return filePart.headers().getContentType() != null ? 
            filePart.headers().getContentType().toString() : "";
    }
    
    /**
     * Formats file size in human-readable format
     * @param bytes The file size in bytes
     * @return Formatted file size string
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
    
    /**
     * Generates the public URL for accessing the uploaded file
     * @param category The file category ("media" or "file")
     * @param module The module name
     * @param fileName The saved filename
     * @return The public URL
     */
    public static String generatePublicUrl(String category, String module, String fileName) {
        return String.format("/uploads/%ss/%s/%s", category, module.toUpperCase(), fileName);
    }
}
