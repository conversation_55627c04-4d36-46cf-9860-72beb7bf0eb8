services:
  nginx-proxy:
    container_name: nginx-proxy
    image: nginx
    restart: unless-stopped
    volumes:
      - ./nginx/proxy/conf.d/:/etc/nginx/conf.d/
      - ./uploads/:/var/www/uploads/:ro
    ports:
      - "80:80"
    networks:
      backend:
        aliases:
          - "nginxproxy"
  
  services-discovery:
    container_name: services-discovery
    image: phhuan/tti:services-discovery-dev
    restart: unless-stopped
    volumes:
      - ./resources/services_discovery.properties:/app/application.properties
    ports:
      - "4000:4000"
    networks:
      backend:
        aliases:
          - "servicesdiscovery"

  api-gateway:
    container_name: api-gateway
    image: phhuan/tti:api-gateway-dev
    restart: unless-stopped
    volumes:
      - ./resources/api_gateway.properties:/app/application.properties
    ports:
      - "4001:4001"
    networks:
      backend:
        aliases:
          - "apigateway"

  id-service:
    container_name: tti-id-service
    image: phhuan/tti:id-service-dev
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./resources/tti_id_service.properties:/app/application.properties
    ports:
      - "4002:4002"
    networks:
      backend:
        aliases:
          - "ttiidservice"

  common-service:
    container_name: common-service
    image: phhuan/tti:common-service-dev
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./resources/common_service.properties:/app/application.properties
    ports:
      - "4004:4004"
    networks:
      backend:
        aliases:
          - "commonservice"

  ohcrm-service:
    container_name: ohcrm-service
    image: phhuan/tti:ohcrm-service-dev
    restart: unless-stopped
    user: "${DOCKER_UID:-1000}:${DOCKER_GID:-1000}"  # Use host user ID to avoid permission issues
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./resources/ohcrm_service.properties:/app/application.properties
      - ./org_dbs/:/app/org_dbs/
      - ./uploads/:/app/uploads/:rw
    ports:
      - "4003:4003"
    networks:
      backend:
        aliases:
          - "ohcrmservice"

  id-web:
    container_name: id-web
    image: phhuan/tti:tti-id-web-dev
    restart: unless-stopped
    volumes:
      - ./nginx/tti-id-web/conf.d/:/etc/nginx/http.d/
    ports:
      - "8000:8000"
    networks:
      backend:
        aliases:
          - "idweb"

  ohcrm-web:
    container_name: ohcrm-web
    image: phhuan/tti:ohcrm-web-dev
    restart: unless-stopped
    volumes:
      - ./nginx/ohcrm-web/conf.d/:/etc/nginx/http.d/
    ports:
      - "8001:8001"
    networks:
      backend:
        aliases:
          - "ohcrmweb"
networks:
  backend:
    driver: bridge
