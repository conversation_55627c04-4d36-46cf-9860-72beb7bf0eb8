#!/bin/bash

echo "=== Fixing uploads directory permissions ==="
echo ""

# Check current permissions
echo "Current permissions:"
ls -la ./uploads/

echo ""
echo "Fixing permissions to allow Docker container to write..."

# Set all directories to 777 (read/write/execute for everyone)
find ./uploads -type d -exec chmod 777 {} \;

# Set all files to 666 (read/write for everyone)
find ./uploads -type f -exec chmod 666 {} \; 2>/dev/null || true

echo ""
echo "Updated permissions:"
ls -la ./uploads/

echo ""
echo "=== Permissions fixed! ==="
echo "You can now test the upload APIs."
