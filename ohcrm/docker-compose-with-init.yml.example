# Alternative docker-compose configuration using init container for permissions
# This is an example - use this if the main solution doesn't work

services:
  # Init container to set up permissions
  ohcrm-service-init:
    image: alpine:latest
    user: "0:0"  # Only the init container runs as root
    volumes:
      - ./uploads/:/app/uploads/:rw
    command: |
      sh -c "
        echo 'Setting up upload directory permissions...'
        mkdir -p /app/uploads/medias /app/uploads/files
        chown -R 1000:1000 /app/uploads
        chmod -R 755 /app/uploads
        echo 'Permissions set successfully'
      "

  ohcrm-service:
    container_name: ohcrm-service
    image: phhuan/tti:ohcrm-service-dev
    restart: unless-stopped
    user: "1000:1000"  # Run as non-root user
    depends_on:
      - ohcrm-service-init
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./resources/ohcrm_service.properties:/app/application.properties
      - ./org_dbs/:/app/org_dbs/
      - ./uploads/:/app/uploads/:rw
    ports:
      - "4003:4003"
    networks:
      backend:
        aliases:
          - "ohcrmservice"
