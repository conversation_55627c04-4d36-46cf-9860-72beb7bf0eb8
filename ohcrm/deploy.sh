#!/bin/bash

# Setup uploads directory permissions before deployment
echo "Setting up uploads directory permissions..."
if [ ! -d "./uploads" ]; then
    echo "Creating uploads directory..."
    mkdir -p ./uploads
fi

# Create subdirectory structure for file organization
echo "Creating subdirectory structure..."
mkdir -p ./uploads/medias
mkdir -p ./uploads/files

# Create common module directories to avoid permission issues
echo "Creating common module directories..."
for module in PLAYBOOK LEAD OPPORTUNITY CONTACT ACCOUNT CAMPAIGN TASK EVENT; do
    mkdir -p "./uploads/medias/$module"
    mkdir -p "./uploads/files/$module"
done

# Set proper permissions for uploads directory and all subdirectories
echo "Setting permissions..."
chmod -R 777 ./uploads

echo "Set up uploads directory permissions successfully!"

if [ $# -eq 0 ]
then
    docker ps -f name=services-discovery -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=api-gateway -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=tti-id-service -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=ohcrm-service -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=common-service -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=ohcrm-web -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=id-web -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=nginx-proxy -q | xargs --no-run-if-empty docker container stop
    docker container ls -a -fname=services-discovery -q | xargs -r docker container rm
    docker container ls -a -fname=api-gateway -q | xargs -r docker container rm
    docker container ls -a -fname=tti-id-service -q | xargs -r docker container rm
    docker container ls -a -fname=ohcrm-service -q | xargs -r docker container rm
    docker container ls -a -fname=common-service -q | xargs -r docker container rm
    docker container ls -a -fname=ohcrm-web -q | xargs -r docker container rm
    docker container ls -a -fname=id-web -q | xargs -r docker container rm
    docker container ls -a -fname=nginx-proxy -q | xargs -r docker container rm
    docker rmi phhuan/tti:services-discovery-dev
    docker rmi phhuan/tti:api-gateway-dev
    docker rmi phhuan/tti:id-service-dev
    docker rmi phhuan/tti:ohcrm-service-dev
    docker rmi phhuan/tti:common-service-dev
    docker rmi phhuan/tti:ohcrm-web-dev
    docker rmi phhuan/tti:tti-id-web-dev
    docker compose up -d
else
    docker ps -f name=$1 -q | xargs --no-run-if-empty docker container stop
    docker container ls -a -fname=$1 -q | xargs -r docker container rm
    #docker rmi phhuan/tti:$1-dev
    docker compose up -d $1
fi
